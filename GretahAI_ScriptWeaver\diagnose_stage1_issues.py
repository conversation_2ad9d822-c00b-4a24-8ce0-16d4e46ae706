#!/usr/bin/env python3
"""
Diagnostic script for Stage 1 redirection and unresponsiveness issues.

This script analyzes the state management, stage transition logic, and reset processes
to identify potential causes of:
1. Unexpected automatic redirection to Stage 1
2. Stage 1 unresponsiveness after redirection
3. Session state management issues
"""

import logging
import os
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("diagnose_stage1_issues")


def analyze_stage_transition_logic():
    """Analyze the stage transition logic for potential issues."""
    logger.info("=== ANALYZING STAGE TRANSITION LOGIC ===")
    
    issues_found = []
    
    try:
        # Check StateManager's advance_to method
        with open('state_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for potential issues in stage transition logic
        lines = content.split('\n')
        
        # Check for fallback to Stage 1
        stage1_fallbacks = []
        for i, line in enumerate(lines):
            if 'StateStage.STAGE1_UPLOAD' in line and ('fallback' in line.lower() or 'default' in line.lower()):
                stage1_fallbacks.append(i + 1)
        
        if stage1_fallbacks:
            logger.warning(f"Found {len(stage1_fallbacks)} potential Stage 1 fallbacks at lines: {stage1_fallbacks}")
            issues_found.append(f"Stage 1 fallbacks found at lines: {stage1_fallbacks}")
        
        # Check for complete reset conditions
        complete_reset_lines = []
        for i, line in enumerate(lines):
            if 'is_complete_reset' in line or ('target_stage_num == 1' in line and 'current_stage_num >= 4' in line):
                complete_reset_lines.append(i + 1)
        
        if complete_reset_lines:
            logger.info(f"Found complete reset logic at lines: {complete_reset_lines}")
        
        # Check update_stage_based_on_completion method
        auto_stage_detection = []
        for i, line in enumerate(lines):
            if 'update_stage_based_on_completion' in line:
                auto_stage_detection.append(i + 1)
        
        if auto_stage_detection:
            logger.info(f"Found automatic stage detection logic at lines: {auto_stage_detection}")
        
        logger.info("✅ Stage transition logic analysis complete")
        
    except Exception as e:
        logger.error(f"❌ Error analyzing stage transition logic: {e}")
        issues_found.append(f"Error analyzing stage transition logic: {e}")
    
    return issues_found


def analyze_state_reset_processes():
    """Analyze state reset processes for potential issues."""
    logger.info("=== ANALYZING STATE RESET PROCESSES ===")
    
    issues_found = []
    
    try:
        # Check StateManager reset methods
        with open('state_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Check reset_test_case_state method
        reset_test_case_lines = []
        in_reset_method = False
        for i, line in enumerate(lines):
            if 'def reset_test_case_state' in line:
                in_reset_method = True
                reset_test_case_lines.append(i + 1)
            elif in_reset_method and line.strip().startswith('def '):
                in_reset_method = False
            elif in_reset_method and 'advance_to(StateStage.STAGE3_CONVERT' in line:
                logger.info(f"Found test case reset advancing to Stage 3 at line {i + 1}")
        
        # Check for any reset methods that might advance to Stage 1
        stage1_resets = []
        for i, line in enumerate(lines):
            if 'advance_to(StateStage.STAGE1_UPLOAD' in line:
                stage1_resets.append(i + 1)
        
        if stage1_resets:
            logger.warning(f"Found direct Stage 1 advances at lines: {stage1_resets}")
            issues_found.append(f"Direct Stage 1 advances found at lines: {stage1_resets}")
        
        logger.info("✅ State reset processes analysis complete")
        
    except Exception as e:
        logger.error(f"❌ Error analyzing state reset processes: {e}")
        issues_found.append(f"Error analyzing state reset processes: {e}")
    
    return issues_found


def analyze_stage1_implementation():
    """Analyze Stage 1 implementation for responsiveness issues."""
    logger.info("=== ANALYZING STAGE 1 IMPLEMENTATION ===")
    
    issues_found = []
    
    try:
        # Check Stage 1 implementation
        with open('stages/stage1.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Check for blocking operations
        blocking_patterns = [
            'while True',
            'time.sleep',
            'input(',
            'raw_input(',
            'sys.stdin.read',
            'blocking=True'
        ]
        
        blocking_issues = []
        for i, line in enumerate(lines):
            for pattern in blocking_patterns:
                if pattern in line and not line.strip().startswith('#'):
                    blocking_issues.append((i + 1, pattern))
        
        if blocking_issues:
            logger.warning(f"Found potential blocking operations in Stage 1:")
            for line_num, pattern in blocking_issues:
                logger.warning(f"  Line {line_num}: {pattern}")
            issues_found.append(f"Blocking operations found: {blocking_issues}")
        
        # Check for missing st.rerun() calls
        rerun_calls = []
        advance_to_calls = []
        for i, line in enumerate(lines):
            if 'st.rerun()' in line:
                rerun_calls.append(i + 1)
            if 'advance_to(' in line:
                advance_to_calls.append(i + 1)
        
        logger.info(f"Found {len(rerun_calls)} st.rerun() calls at lines: {rerun_calls}")
        logger.info(f"Found {len(advance_to_calls)} advance_to() calls at lines: {advance_to_calls}")
        
        # Check for proper error handling
        try_except_blocks = []
        for i, line in enumerate(lines):
            if line.strip().startswith('try:'):
                try_except_blocks.append(i + 1)
        
        logger.info(f"Found {len(try_except_blocks)} try-except blocks at lines: {try_except_blocks}")
        
        logger.info("✅ Stage 1 implementation analysis complete")
        
    except Exception as e:
        logger.error(f"❌ Error analyzing Stage 1 implementation: {e}")
        issues_found.append(f"Error analyzing Stage 1 implementation: {e}")
    
    return issues_found


def analyze_session_state_management():
    """Analyze session state management for potential issues."""
    logger.info("=== ANALYZING SESSION STATE MANAGEMENT ===")
    
    issues_found = []
    
    try:
        # Check app.py for session state handling
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Check for session state transitions
        session_transitions = []
        for i, line in enumerate(lines):
            if 'transitioning_' in line and 'st.session_state' in line:
                session_transitions.append(i + 1)
        
        logger.info(f"Found {len(session_transitions)} session state transitions at lines: {session_transitions}")
        
        # Check for StateManager initialization
        init_calls = []
        for i, line in enumerate(lines):
            if 'StateManager().init_in_session' in line:
                init_calls.append(i + 1)
        
        if init_calls:
            logger.info(f"Found StateManager initialization at lines: {init_calls}")
        else:
            logger.warning("No StateManager initialization found")
            issues_found.append("No StateManager initialization found")
        
        # Check for fallback logic in _get_current_stage_number
        fallback_logic = []
        for i, line in enumerate(lines):
            if 'fallback' in line.lower() and 'stage' in line.lower():
                fallback_logic.append(i + 1)
        
        if fallback_logic:
            logger.warning(f"Found fallback logic at lines: {fallback_logic}")
            issues_found.append(f"Fallback logic found at lines: {fallback_logic}")
        
        logger.info("✅ Session state management analysis complete")
        
    except Exception as e:
        logger.error(f"❌ Error analyzing session state management: {e}")
        issues_found.append(f"Error analyzing session state management: {e}")
    
    return issues_found


def analyze_flag_cleanup():
    """Analyze flag cleanup utilities for potential issues."""
    logger.info("=== ANALYZING FLAG CLEANUP ===")
    
    issues_found = []
    
    try:
        # Check flag helpers
        with open('utils/flag_helpers.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Check cleanup_one_shot_flags implementation
        cleanup_implementation = []
        for i, line in enumerate(lines):
            if 'cleanup_one_shot_flags' in line:
                cleanup_implementation.append(i + 1)
        
        logger.info(f"Found flag cleanup implementation at lines: {cleanup_implementation}")
        
        # Check for potential cleanup issues
        cleanup_issues = []
        for i, line in enumerate(lines):
            if 'del st.session_state' in line:
                cleanup_issues.append(i + 1)
        
        logger.info(f"Found session state deletions at lines: {cleanup_issues}")
        
        logger.info("✅ Flag cleanup analysis complete")
        
    except Exception as e:
        logger.error(f"❌ Error analyzing flag cleanup: {e}")
        issues_found.append(f"Error analyzing flag cleanup: {e}")
    
    return issues_found


def main():
    """Main diagnostic function."""
    logger.info("=== STAGE 1 REDIRECTION AND UNRESPONSIVENESS DIAGNOSTIC ===")
    
    all_issues = []
    
    # Run all analyses
    all_issues.extend(analyze_stage_transition_logic())
    all_issues.extend(analyze_state_reset_processes())
    all_issues.extend(analyze_stage1_implementation())
    all_issues.extend(analyze_session_state_management())
    all_issues.extend(analyze_flag_cleanup())
    
    # Summary
    logger.info("=== DIAGNOSTIC SUMMARY ===")
    
    if all_issues:
        logger.warning(f"Found {len(all_issues)} potential issues:")
        for i, issue in enumerate(all_issues, 1):
            logger.warning(f"{i}. {issue}")
    else:
        logger.info("✅ No obvious issues found in static analysis")
    
    # Recommendations
    logger.info("=== RECOMMENDATIONS ===")
    logger.info("1. Check application logs for StateManager initialization warnings")
    logger.info("2. Monitor session state transitions during redirection events")
    logger.info("3. Verify that Stage 1 file upload processing completes successfully")
    logger.info("4. Check for any infinite loops or blocking operations in Stage 1")
    logger.info("5. Ensure proper error handling in file upload and parsing logic")
    
    return len(all_issues) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
