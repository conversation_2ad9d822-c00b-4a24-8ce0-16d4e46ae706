#!/usr/bin/env python3
"""
Verification script for Stage 8 → Stage 3 automatic progression implementation.

This script verifies that the automatic stage progression from Stage 8 (Script Optimization)
to Stage 3 (Test Case Analysis and Conversion) works correctly after optimization completion.
"""

import logging
from unittest.mock import Mock, patch
from state_manager import StateManager, StateStage

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("verify_stage8_to_stage3")


def test_automatic_stage8_to_stage3_progression():
    """Test automatic Stage 8 → Stage 3 progression after optimization completion."""
    logger.info("=" * 80)
    logger.info("TESTING AUTOMATIC STAGE 8 → STAGE 3 PROGRESSION")
    logger.info("=" * 80)
    
    # Create a mock state manager
    state = Mock(spec=StateManager)
    state.current_stage = StateStage.STAGE8_OPTIMIZE
    state.advance_to = Mock(return_value=True)
    
    # Set up optimization completion state
    state.optimization_complete = True
    state.optimized_script_path = "generated_tests/test_TC001_optimized_12345.py"
    state.optimized_script_content = "# Optimized test script content"
    state.optimization_in_progress = False
    
    # Mock session state
    mock_session_state = {}
    
    logger.info("Test setup complete:")
    logger.info(f"  - Current stage: {state.current_stage}")
    logger.info(f"  - Optimization complete: {state.optimization_complete}")
    logger.info(f"  - Optimized script path: {state.optimized_script_path}")
    
    # Simulate the automatic progression logic from stage8.py
    with patch('streamlit.session_state', mock_session_state):
        with patch('streamlit.rerun') as mock_rerun:
            logger.info("Simulating optimization completion logic...")
            
            # This is the exact logic from stage8.py lines 1079-1091
            if state.current_stage == StateStage.STAGE8_OPTIMIZE:
                state.advance_to(StateStage.STAGE3_CONVERT, "Script optimization completed - automatically advancing to Stage 3 for new test case selection")
                
                # Force state update in session state
                mock_session_state['state'] = state
                mock_session_state['stage_progression_message'] = "✅ Script optimization completed successfully. Proceeding to Stage 3 to select a new test case."
                
                logger.info("Stage 8: Automatically advancing to Stage 3 after optimization completion")
                
                # Call st.rerun() to immediately refresh the UI
                mock_rerun()
    
    # Verify the transition was called correctly
    logger.info("Verifying automatic progression results...")
    
    expected_stage = StateStage.STAGE3_CONVERT
    expected_message = "Script optimization completed - automatically advancing to Stage 3 for new test case selection"
    
    # Check that advance_to was called with correct parameters
    state.advance_to.assert_called_once_with(expected_stage, expected_message)
    logger.info("✅ advance_to() called with correct parameters")
    
    # Check that st.rerun() was called
    mock_rerun.assert_called_once()
    logger.info("✅ st.rerun() called to refresh UI")
    
    # Check session state was updated correctly
    assert mock_session_state['stage_progression_message'] == "✅ Script optimization completed successfully. Proceeding to Stage 3 to select a new test case."
    logger.info("✅ Session state progression message set correctly")
    
    assert mock_session_state['state'] == state
    logger.info("✅ Session state updated with new state")
    
    logger.info("=" * 80)
    logger.info("✅ AUTOMATIC STAGE 8 → STAGE 3 PROGRESSION TEST PASSED")
    logger.info("=" * 80)
    
    return True


def test_state_manager_stage8_to_stage3_support():
    """Test that StateManager properly supports Stage 8 → Stage 3 transitions."""
    logger.info("=" * 80)
    logger.info("TESTING STATE MANAGER STAGE 8 → STAGE 3 SUPPORT")
    logger.info("=" * 80)
    
    # Create a real StateManager instance
    state = StateManager()
    
    # Set up Stage 8 state
    state.current_stage = StateStage.STAGE8_OPTIMIZE
    state.optimization_complete = True
    state.optimized_script_path = "test_script.py"
    
    logger.info(f"Initial stage: {state.current_stage}")
    
    # Test direct advance_to call
    result = state.advance_to(StateStage.STAGE3_CONVERT, "Test Stage 8 → Stage 3 transition")
    
    # Verify transition was successful
    assert result == True, "advance_to should return True for legal transition"
    logger.info("✅ advance_to() returned True for Stage 8 → Stage 3 transition")
    
    assert state.current_stage == StateStage.STAGE3_CONVERT, f"Expected Stage 3, got {state.current_stage}"
    logger.info("✅ Current stage updated to Stage 3")
    
    # Test reset_test_case_state method
    state.current_stage = StateStage.STAGE8_OPTIMIZE  # Reset to Stage 8
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.optimization_complete = True
    
    logger.info("Testing reset_test_case_state method...")
    result = state.reset_test_case_state(confirm=True, reason="Script optimization complete")
    
    assert result == True, "reset_test_case_state should return True"
    logger.info("✅ reset_test_case_state() returned True")
    
    assert state.current_stage == StateStage.STAGE3_CONVERT, f"Expected Stage 3 after reset, got {state.current_stage}"
    logger.info("✅ reset_test_case_state() correctly advanced to Stage 3")
    
    # Verify optimization state was cleared
    assert state.optimization_complete == False, "optimization_complete should be cleared"
    logger.info("✅ Optimization state properly cleared")
    
    assert state.selected_test_case == None, "selected_test_case should be cleared"
    logger.info("✅ Test case state properly cleared")
    
    logger.info("=" * 80)
    logger.info("✅ STATE MANAGER STAGE 8 → STAGE 3 SUPPORT TEST PASSED")
    logger.info("=" * 80)
    
    return True


def main():
    """Run all verification tests."""
    logger.info("Starting Stage 8 → Stage 3 automatic progression verification...")
    
    try:
        # Test 1: Automatic progression logic
        test_automatic_stage8_to_stage3_progression()
        
        # Test 2: StateManager support
        test_state_manager_stage8_to_stage3_support()
        
        logger.info("🎉 ALL VERIFICATION TESTS PASSED!")
        logger.info("✅ Stage 8 → Stage 3 automatic progression is working correctly")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
