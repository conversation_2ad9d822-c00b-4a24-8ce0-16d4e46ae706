#!/usr/bin/env python3
"""
Test script to verify that all buttons in Stage 6 have unique keys.

This script analyzes the Stage 6 code to ensure there are no duplicate button IDs
that could cause StreamlitDuplicateElementId errors.
"""

import re
import sys
import os

def extract_button_keys_from_file(file_path):
    """Extract all button keys from a Python file."""
    button_keys = []
    button_patterns = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Pattern to match st.button() calls with key parameters
        button_pattern = r'st\.button\([^)]*key\s*=\s*["\']([^"\']+)["\'][^)]*\)'
        matches = re.findall(button_pattern, content)

        for match in matches:
            button_keys.append(match)

        # Pattern to match st.download_button() calls with key parameters
        download_button_pattern = r'st\.download_button\([^)]*key\s*=\s*["\']([^"\']+)["\'][^)]*\)'
        download_matches = re.findall(download_button_pattern, content)

        for match in download_matches:
            button_keys.append(match)

        # Also find buttons without keys (potential issues)
        button_without_key_pattern = r'st\.button\((?![^)]*key\s*=)[^)]+\)'
        no_key_matches = re.findall(button_without_key_pattern, content)

        # Also find download buttons without keys
        download_without_key_pattern = r'st\.download_button\((?![^)]*key\s*=)[^)]+\)'
        download_no_key_matches = re.findall(download_without_key_pattern, content)

        # Extract line numbers for buttons without keys
        lines = content.split('\n')
        buttons_without_keys = []

        for i, line in enumerate(lines, 1):
            if ('st.button(' in line and 'key=' not in line) or ('st.download_button(' in line and 'key=' not in line):
                buttons_without_keys.append((i, line.strip()))

        return button_keys, buttons_without_keys

    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return [], []

def check_duplicate_keys(button_keys):
    """Check for duplicate button keys."""
    seen_keys = set()
    duplicates = []

    for key in button_keys:
        if key in seen_keys:
            duplicates.append(key)
        else:
            seen_keys.add(key)

    return duplicates

def main():
    """Test button keys in Stage 6."""
    print("🔍 Testing Button Keys in Stage 6")
    print("=" * 50)

    stage6_file = "stages/stage6.py"

    if not os.path.exists(stage6_file):
        print(f"❌ File not found: {stage6_file}")
        return False

    # Extract button keys
    button_keys, buttons_without_keys = extract_button_keys_from_file(stage6_file)

    print(f"📊 Found {len(button_keys)} buttons/download buttons with keys")
    print(f"⚠️  Found {len(buttons_without_keys)} buttons/download buttons without keys")

    # Check for duplicates
    duplicates = check_duplicate_keys(button_keys)

    print("\n🔑 Button Keys Found:")
    for i, key in enumerate(sorted(button_keys), 1):
        print(f"  {i:2d}. {key}")

    if buttons_without_keys:
        print("\n⚠️  Buttons Without Keys (Potential Issues):")
        for line_num, line_content in buttons_without_keys:
            print(f"  Line {line_num:4d}: {line_content}")

    if duplicates:
        print(f"\n❌ Duplicate Keys Found:")
        for duplicate in duplicates:
            print(f"  - {duplicate}")
        return False
    else:
        print(f"\n✅ No duplicate keys found!")

    # Summary
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print(f"  • Total buttons with keys: {len(button_keys)}")
    print(f"  • Buttons without keys: {len(buttons_without_keys)}")
    print(f"  • Duplicate keys: {len(duplicates)}")

    if len(duplicates) == 0 and len(buttons_without_keys) == 0:
        print("✅ ALL TESTS PASSED - No duplicate button ID issues!")
        return True
    elif len(duplicates) == 0:
        print("⚠️  PARTIAL SUCCESS - No duplicates, but some buttons lack keys")
        print("💡 Consider adding keys to buttons without keys for consistency")
        return True
    else:
        print("❌ TESTS FAILED - Duplicate button keys found!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
