#!/usr/bin/env python3
"""
Verification script for StateStage import status in stages/stage8.py.

This script verifies that Stage 8 does NOT have the same UnboundLocalError issue
that was found and fixed in Stage 7. It checks:
1. StateStage is properly imported at module level
2. No redundant local imports exist inside functions
3. StateStage can be referenced correctly in all stage transition logic
"""

import logging
import os
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("verify_stage8_import_status")


def check_module_level_imports(file_path):
    """Check if StateStage is imported at module level."""
    logger.info(f"Checking module-level imports in {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check manually by looking at the first 50 lines
        lines = content.split('\n')[:50]
        for i, line in enumerate(lines):
            if 'from state_manager import StateStage' in line and not line.strip().startswith('#'):
                logger.info(f"✅ Found module-level StateStage import at line {i+1} in {file_path}")
                return True
        
        logger.error(f"❌ No module-level StateStage import found in {file_path}")
        return False
        
    except Exception as e:
        logger.error(f"❌ Error checking {file_path}: {e}")
        return False


def check_redundant_local_imports(file_path):
    """Check for redundant local imports of StateStage inside functions."""
    logger.info(f"Checking for redundant local imports in {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for local imports inside functions
        lines = content.split('\n')
        redundant_imports = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if 'from state_manager import StateStage' in stripped and not stripped.startswith('#'):
                # Check if this line is indented (likely inside a function)
                if line.startswith('    ') or line.startswith('\t'):
                    redundant_imports.append(i + 1)
                    logger.warning(f"⚠️ Found potential redundant local import at line {i+1}: {stripped}")
        
        if redundant_imports:
            logger.error(f"❌ Found {len(redundant_imports)} redundant local imports in {file_path}")
            return False
        else:
            logger.info(f"✅ No redundant local imports found in {file_path}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error checking {file_path}: {e}")
        return False


def check_stastage_references(file_path):
    """Check that all StateStage references are valid."""
    logger.info(f"Checking StateStage references in {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count StateStage references
        lines = content.split('\n')
        references = []
        
        for i, line in enumerate(lines):
            if 'StateStage.' in line and not line.strip().startswith('#'):
                references.append(i + 1)
        
        logger.info(f"✅ Found {len(references)} StateStage references in {file_path}")
        
        # Check specific lines that use StateStage
        for line_num in references:
            if line_num <= len(lines):
                line_content = lines[line_num - 1]
                logger.info(f"✅ Line {line_num} StateStage reference: {line_content.strip()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking StateStage references: {e}")
        return False


def test_import_functionality():
    """Test that the stage8 module can be imported without errors."""
    logger.info("Testing stage8 module import functionality...")
    
    try:
        # Add the current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Try to import the stage8 module
        import stages.stage8
        logger.info("✅ stages.stage8 imported successfully")
        
        # Check if the main function exists
        if hasattr(stages.stage8, 'stage8_optimize_script'):
            logger.info("✅ stage8_optimize_script function found")
        else:
            logger.warning("⚠️ stage8_optimize_script function not found")
        
        # Check if StateStage is accessible
        from state_manager import StateStage
        logger.info("✅ StateStage imported successfully")
        
        # Test that StateStage enum values are accessible
        test_stages = [
            StateStage.STAGE3_CONVERT,
            StateStage.STAGE8_OPTIMIZE
        ]
        logger.info(f"✅ StateStage enum values accessible: {[stage.name for stage in test_stages]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def main():
    """Main verification function."""
    logger.info("=== Stage 8 StateStage Import Status Verification ===")
    
    stage8_file = "stages/stage8.py"
    
    if not os.path.exists(stage8_file):
        logger.error(f"❌ {stage8_file} not found")
        return False
    
    all_passed = True
    
    # Check module-level import
    has_module_import = check_module_level_imports(stage8_file)
    if not has_module_import:
        logger.error(f"❌ {stage8_file} missing module-level StateStage import")
        all_passed = False
    
    # Check for redundant local imports
    no_redundant_imports = check_redundant_local_imports(stage8_file)
    if not no_redundant_imports:
        logger.error(f"❌ {stage8_file} has redundant local imports")
        all_passed = False
    
    # Check StateStage references
    valid_references = check_stastage_references(stage8_file)
    if not valid_references:
        logger.error(f"❌ {stage8_file} has invalid StateStage references")
        all_passed = False
    
    # Test import functionality
    import_works = test_import_functionality()
    if not import_works:
        logger.error(f"❌ {stage8_file} import functionality failed")
        all_passed = False
    
    if all_passed:
        logger.info("🎉 All verification tests passed! Stage 8 StateStage imports are working correctly.")
        logger.info("✅ Stage 8 does NOT have the same UnboundLocalError issue that was found in Stage 7.")
        logger.info("✅ Stage 8 is properly implemented with correct StateStage import structure.")
    else:
        logger.error("❌ Some verification tests failed. Please review the issues above.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
