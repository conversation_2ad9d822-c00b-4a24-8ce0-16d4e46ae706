#!/usr/bin/env python3
"""
Test script to verify Stage 1 to Stage 2 transition fix.

This script tests the StateStage import fix and stage progression logic
to ensure CSV upload properly triggers automatic advancement to Stage 2.
"""

import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_stage1_transition")


def test_stage1_imports():
    """Test that Stage 1 has correct StateStage imports."""
    logger.info("=== TESTING STAGE 1 IMPORTS ===")
    
    try:
        # Import stage1 module to check for import errors
        sys.path.insert(0, 'stages')
        from stages.stage1 import stage1_upload_excel
        
        # Check if StateStage is accessible
        from state_manager import StateStage
        
        # Verify StateStage enum values
        assert StateStage.STAGE1_UPLOAD.value == "stage1_upload"
        assert StateStage.STAGE2_WEBSITE.value == "stage2_website"
        
        logger.info("✅ Stage 1 imports working correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Stage 1 import test failed: {e}")
        return False


def test_stage_transition_logic():
    """Test the stage transition logic."""
    logger.info("=== TESTING STAGE TRANSITION LOGIC ===")
    
    try:
        from state_manager import StateManager, StateStage
        
        # Create a test state manager
        state = StateManager()
        
        # Verify initial state
        assert state.current_stage == StateStage.STAGE1_UPLOAD
        logger.info(f"✅ Initial stage: {state.current_stage.get_display_name()}")
        
        # Test Stage 1 → Stage 2 transition
        success = state.advance_to(StateStage.STAGE2_WEBSITE, "Test CSV upload completed")
        
        if success:
            assert state.current_stage == StateStage.STAGE2_WEBSITE
            logger.info(f"✅ Stage transition successful: {state.current_stage.get_display_name()}")
            return True
        else:
            logger.error("❌ Stage transition failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Stage transition test failed: {e}")
        return False


def test_csv_upload_simulation():
    """Simulate CSV upload and test stage progression."""
    logger.info("=== TESTING CSV UPLOAD SIMULATION ===")
    
    try:
        from state_manager import StateManager, StateStage
        
        # Create a test state manager
        state = StateManager()
        
        # Simulate successful CSV upload
        state.uploaded_excel = "test_file.xlsx"
        state.test_cases = [
            {"Test Case ID": "TC001", "Test Steps": "Login", "Expected Result": "Success"},
            {"Test Case ID": "TC002", "Test Steps": "Navigate", "Expected Result": "Page loads"}
        ]
        
        # Test the stage progression logic that should happen after CSV upload
        if state.current_stage == StateStage.STAGE1_UPLOAD:
            success = state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {len(state.test_cases)} test cases")
            
            if success:
                logger.info(f"✅ CSV upload simulation successful: {len(state.test_cases)} test cases loaded")
                logger.info(f"✅ Advanced to: {state.current_stage.get_display_name()}")
                return True
            else:
                logger.error("❌ Failed to advance after CSV upload simulation")
                return False
        else:
            logger.error(f"❌ Unexpected initial stage: {state.current_stage.get_display_name()}")
            return False
            
    except Exception as e:
        logger.error(f"❌ CSV upload simulation failed: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🧪 Testing Stage 1 to Stage 2 transition fix...")
    
    all_tests_passed = True
    
    # Test 1: Import validation
    if not test_stage1_imports():
        all_tests_passed = False
    
    # Test 2: Stage transition logic
    if not test_stage_transition_logic():
        all_tests_passed = False
    
    # Test 3: CSV upload simulation
    if not test_csv_upload_simulation():
        all_tests_passed = False
    
    # Final result
    if all_tests_passed:
        logger.info("🎉 ALL TESTS PASSED - Stage 1 transition fix verified!")
        return True
    else:
        logger.error("💥 SOME TESTS FAILED - Stage 1 transition fix needs attention")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
