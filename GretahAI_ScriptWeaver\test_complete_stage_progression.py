#!/usr/bin/env python3
"""
Test script to verify complete stage progression from Stage 1 to Stage 3.

This script tests that both Stage 1 → Stage 2 and Stage 2 → Stage 3 transitions
work correctly with the st.rerun() fixes applied.
"""

import sys
import os
sys.path.append('.')

from state_manager import StateManager, StateStage
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('test_complete_stage_progression')

def test_complete_stage_progression():
    """Test complete stage progression from Stage 1 to Stage 3."""
    
    print("=== Testing Complete Stage Progression (Stage 1 → Stage 2 → Stage 3) ===")
    
    # Create a mock streamlit session state
    class MockST:
        def __init__(self):
            self.session_state = {}

    mock_st = MockST()

    # Initialize state manager
    state_manager = StateManager()
    state_manager.init_in_session(mock_st)
    state = StateManager.get(mock_st)

    print(f"Initial state: {state.current_stage.get_display_name()}")
    
    # Test 1: Verify initial stage is Stage 1
    assert state.current_stage == StateStage.STAGE1_UPLOAD, f"Expected Stage 1, got {state.current_stage}"
    print("✅ Test 1 passed: Initial stage is Stage 1")
    
    # Test 2: Simulate file upload and Stage 1 → Stage 2 transition
    print("\n--- Testing Stage 1 → Stage 2 Transition ---")
    
    # Set up file upload state
    state.uploaded_excel = "test_file.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}, {"Test Case ID": "TC002"}]
    
    # Simulate Stage 1 completion logic
    if state.current_stage == StateStage.STAGE1_UPLOAD:
        result = state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {len(state.test_cases)} test cases")
        assert result == True, "Stage 1 → Stage 2 advancement should succeed"
        print("✅ Test 2 passed: Stage 1 → Stage 2 transition successful")
    
    # Verify we're now in Stage 2
    assert state.current_stage == StateStage.STAGE2_WEBSITE, f"Expected Stage 2, got {state.current_stage}"
    print(f"✅ Current stage: {state.current_stage.get_display_name()}")
    
    # Test 3: Simulate website URL configuration and Stage 2 → Stage 3 transition
    print("\n--- Testing Stage 2 → Stage 3 Transition ---")
    
    # Set up website URL state
    state.website_url = "https://the-internet.herokuapp.com/login"
    
    # Simulate Stage 2 completion logic
    if state.current_stage.get_stage_number() == 2:
        result = state.advance_to(StateStage.STAGE3_CONVERT, f"Website URL configured: {state.website_url}")
        assert result == True, "Stage 2 → Stage 3 advancement should succeed"
        print("✅ Test 3 passed: Stage 2 → Stage 3 transition successful")
    
    # Verify we're now in Stage 3
    assert state.current_stage == StateStage.STAGE3_CONVERT, f"Expected Stage 3, got {state.current_stage}"
    print(f"✅ Current stage: {state.current_stage.get_display_name()}")
    
    # Test 4: Verify state consistency
    print("\n--- Testing State Consistency ---")
    
    # Check that all state is preserved
    assert state.uploaded_excel == "test_file.xlsx", "File upload state should be preserved"
    assert len(state.test_cases) == 2, "Test cases should be preserved"
    assert state.website_url == "https://the-internet.herokuapp.com/login", "Website URL should be preserved"
    print("✅ Test 4 passed: All state is preserved across transitions")
    
    # Test 5: Verify stage transition validation
    print("\n--- Testing Stage Transition Validation ---")
    
    # Try to go backwards illegally (should fail)
    result = state.advance_to(StateStage.STAGE1_UPLOAD, "Illegal backward transition")
    assert result == False, "Illegal backward transition should fail"
    print("✅ Test 5 passed: Illegal transitions are properly blocked")
    
    # Verify we're still in Stage 3
    assert state.current_stage == StateStage.STAGE3_CONVERT, f"Stage should remain Stage 3, got {state.current_stage}"
    print(f"✅ Stage remained stable: {state.current_stage.get_display_name()}")
    
    print("\n=== All Tests Passed! ===")
    print("✅ Stage 1 → Stage 2 transition: WORKING")
    print("✅ Stage 2 → Stage 3 transition: WORKING") 
    print("✅ State persistence: WORKING")
    print("✅ Transition validation: WORKING")
    print("✅ Complete stage progression is functioning correctly!")
    
    return True

def test_stage_progression_conditions():
    """Test the specific conditions that trigger stage progressions."""
    
    print("\n=== Testing Stage Progression Conditions ===")
    
    # Create a mock streamlit session state
    class MockST:
        def __init__(self):
            self.session_state = {}

    mock_st = MockST()

    # Initialize state manager
    state_manager = StateManager()
    state_manager.init_in_session(mock_st)
    state = StateManager.get(mock_st)
    
    # Test Stage 1 completion condition
    print("Testing Stage 1 completion condition...")
    
    # Should NOT advance without test cases
    state.uploaded_excel = "test.xlsx"
    state.test_cases = None
    # This should not trigger advancement (no test cases)
    
    # Should advance WITH test cases
    state.test_cases = [{"Test Case ID": "TC001"}]
    if state.current_stage == StateStage.STAGE1_UPLOAD and state.test_cases:
        state.advance_to(StateStage.STAGE2_WEBSITE, "Test cases loaded")
        print("✅ Stage 1 completion condition: test_cases present")
    
    # Test Stage 2 completion condition
    print("Testing Stage 2 completion condition...")
    
    # Should NOT advance with default URL
    state.website_url = "https://example.com"
    # This should not trigger advancement (default URL)
    
    # Should advance with valid URL
    state.website_url = "https://real-website.com"
    if (state.current_stage.get_stage_number() == 2 and 
        state.website_url and 
        state.website_url != "https://example.com"):
        state.advance_to(StateStage.STAGE3_CONVERT, f"Website URL configured: {state.website_url}")
        print("✅ Stage 2 completion condition: valid website URL")
    
    assert state.current_stage == StateStage.STAGE3_CONVERT, "Should be in Stage 3"
    print("✅ All progression conditions working correctly")
    
    return True

if __name__ == "__main__":
    try:
        success1 = test_complete_stage_progression()
        success2 = test_stage_progression_conditions()
        
        if success1 and success2:
            print("\n🎉 ALL TESTS PASSED! Stage progression fixes are working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
