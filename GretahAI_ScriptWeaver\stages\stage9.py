"""
Stage 9: Script Browser and Comparison for GretahAI ScriptWeaver

This module handles the script browsing and comparison phase of the application workflow.
It provides functionality for:
- Browsing all generated scripts from the current session
- Comparing different script versions (original vs optimized)
- Displaying script metadata and generation history
- Searching and filtering scripts by various criteria
- Downloading any script version
- Side-by-side diff views with syntax highlighting

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions (Stage 9 → Stage 3 for new test case)

Functions:
    stage9_browse_scripts(state): Main Stage 9 function for script browsing and comparison
"""

import os
import logging
import streamlit as st
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from state_manager import StateStage

# Import helper functions
from core.script_browser_helpers import (
    filter_scripts_by_criteria,
    search_scripts_by_content,
    generate_script_comparison,
    format_script_metadata,
    get_script_statistics,
    create_download_filename
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage9")


def stage9_browse_scripts(state):
    """
    Stage 9: Script Browser and Comparison.

    This stage allows users to browse, compare, and download all generated scripts
    from the current session. It provides comprehensive script management capabilities
    including filtering, searching, comparison views, and metadata display.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Stage 9: Script Browser and Comparison</h2>", unsafe_allow_html=True)

    # Check if we have any scripts in history
    if not state.script_history:
        st.info("📝 No scripts found in history. Generate some scripts in previous stages to use the browser.")
        
        # Provide navigation back to earlier stages
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔙 Back to Stage 3 (Test Case Selection)", use_container_width=True):
                state.advance_to(StateStage.STAGE3_CONVERT, "User navigated back from Stage 9")
                st.rerun()
                return
        with col2:
            if st.button("🔙 Back to Stage 8 (Optimization)", use_container_width=True):
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated back from Stage 9")
                st.rerun()
                return
        return

    # Display script statistics overview
    with st.expander("📊 Script History Overview", expanded=True):
        stats = get_script_statistics(state.script_history)
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Scripts", stats.get('total_scripts', 0))
        with col2:
            st.metric("Test Cases", stats.get('test_case_count', 0))
        with col3:
            st.metric("Total Lines", stats.get('total_lines', 0))
        with col4:
            st.metric("Total Size", f"{stats.get('total_size', 0)} chars")

        # Script type breakdown
        script_types = stats.get('script_types', {})
        if script_types:
            st.markdown("**Script Types:**")
            type_cols = st.columns(len(script_types))
            for i, (script_type, count) in enumerate(script_types.items()):
                with type_cols[i]:
                    st.metric(script_type.title(), count)

    # Filtering and Search Section
    with st.expander("🔍 Filter and Search Scripts", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            # Filter options
            st.markdown("**Filter Options:**")
            
            # Script type filter
            script_types = list(set(script.get('type', 'unknown') for script in state.script_history))
            selected_type = st.selectbox(
                "Script Type",
                options=['All'] + script_types,
                index=0,
                key="filter_script_type"
            )
            
            # Test case filter
            test_cases = list(set(script.get('test_case_id', 'unknown') for script in state.script_history))
            selected_test_case = st.selectbox(
                "Test Case",
                options=['All'] + test_cases,
                index=0,
                key="filter_test_case"
            )
            
            # Step number filter
            step_numbers = list(set(script.get('step_no', '') for script in state.script_history if script.get('step_no')))
            step_numbers = [s for s in step_numbers if s]  # Remove empty strings
            selected_step = st.selectbox(
                "Step Number",
                options=['All'] + sorted(step_numbers),
                index=0,
                key="filter_step_no"
            )
        
        with col2:
            # Search options
            st.markdown("**Search Options:**")
            
            search_term = st.text_input(
                "Search in script content",
                placeholder="Enter search term...",
                key="search_term"
            )
            
            case_sensitive = st.checkbox(
                "Case sensitive search",
                key="case_sensitive_search"
            )
            
            # Date range filter
            st.markdown("**Date Range:**")
            date_filter = st.selectbox(
                "Filter by date",
                options=['All time', 'Last hour', 'Last 24 hours', 'Last week'],
                index=0,
                key="date_filter"
            )

    # Apply filters and search
    filtered_scripts = state.script_history.copy()
    
    # Apply type filter
    if selected_type != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts, 
            script_type=selected_type
        )
    
    # Apply test case filter
    if selected_test_case != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts, 
            test_case_id=selected_test_case
        )
    
    # Apply step filter
    if selected_step != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts, 
            step_no=selected_step
        )
    
    # Apply date filter
    if date_filter != 'All time':
        now = datetime.now()
        if date_filter == 'Last hour':
            start_date = now - timedelta(hours=1)
        elif date_filter == 'Last 24 hours':
            start_date = now - timedelta(days=1)
        elif date_filter == 'Last week':
            start_date = now - timedelta(weeks=1)
        else:
            start_date = datetime.min
            
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            date_range=(start_date, now)
        )
    
    # Apply search
    if search_term:
        filtered_scripts = search_scripts_by_content(
            filtered_scripts,
            search_term,
            case_sensitive
        )

    # Display filtered results count
    if len(filtered_scripts) != len(state.script_history):
        st.info(f"📋 Showing {len(filtered_scripts)} of {len(state.script_history)} scripts")

    # Script Browser Section
    with st.expander("📜 Script Browser", expanded=True):
        if not filtered_scripts:
            st.warning("No scripts match the current filters.")
        else:
            # Sort scripts by timestamp (newest first)
            sorted_scripts = sorted(filtered_scripts, key=lambda x: x.get('timestamp', datetime.min), reverse=True)
            
            for i, script in enumerate(sorted_scripts):
                with st.container():
                    # Script header with metadata
                    col1, col2, col3 = st.columns([3, 1, 1])
                    
                    with col1:
                        script_title = f"{script.get('type', 'Script').title()} - {script.get('test_case_id', 'Unknown')}"
                        if script.get('step_no'):
                            script_title += f" (Step {script.get('step_no')})"
                        st.markdown(f"**{script_title}**")
                        
                        timestamp = script.get('timestamp', datetime.now())
                        st.caption(f"Generated: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    with col2:
                        # Download button
                        filename = create_download_filename(script)
                        if st.download_button(
                            "📥 Download",
                            data=script.get('content', ''),
                            file_name=filename,
                            mime="text/plain",
                            key=f"download_{script.get('id', i)}"
                        ):
                            st.success(f"✅ Downloaded {filename}")
                    
                    with col3:
                        # View/Compare toggle
                        view_key = f"view_{script.get('id', i)}"
                        if st.button("👁️ View", key=view_key):
                            st.session_state[f"show_script_{i}"] = not st.session_state.get(f"show_script_{i}", False)
                    
                    # Show script content if toggled
                    if st.session_state.get(f"show_script_{i}", False):
                        # Metadata display
                        metadata = format_script_metadata(script)
                        
                        meta_cols = st.columns(3)
                        meta_items = list(metadata.items())
                        for j, (key, value) in enumerate(meta_items):
                            with meta_cols[j % 3]:
                                st.metric(key, value)
                        
                        # Script content with syntax highlighting
                        st.code(script.get('content', ''), language="python")
                    
                    st.divider()

    # Script Comparison Section
    with st.expander("🔄 Script Comparison", expanded=False):
        if len(filtered_scripts) < 2:
            st.info("Need at least 2 scripts to perform comparison.")
        else:
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**Select First Script:**")
                script1_options = [f"{s.get('type', 'Script')} - {s.get('test_case_id', 'Unknown')} ({s.get('timestamp', datetime.now()).strftime('%H:%M:%S')})" 
                                 for s in filtered_scripts]
                script1_idx = st.selectbox(
                    "First script",
                    options=range(len(script1_options)),
                    format_func=lambda x: script1_options[x],
                    key="compare_script1"
                )
            
            with col2:
                st.markdown("**Select Second Script:**")
                script2_idx = st.selectbox(
                    "Second script",
                    options=range(len(script1_options)),
                    format_func=lambda x: script1_options[x],
                    key="compare_script2"
                )
            
            # Comparison type
            comparison_type = st.radio(
                "Comparison Type",
                options=["side_by_side", "unified"],
                format_func=lambda x: "Side by Side" if x == "side_by_side" else "Unified Diff",
                horizontal=True,
                key="comparison_type"
            )
            
            if st.button("🔄 Generate Comparison", use_container_width=True):
                if script1_idx != script2_idx:
                    script1 = filtered_scripts[script1_idx]
                    script2 = filtered_scripts[script2_idx]
                    
                    comparison_html = generate_script_comparison(script1, script2, comparison_type)
                    st.components.v1.html(comparison_html, height=600, scrolling=True)
                else:
                    st.warning("Please select two different scripts for comparison.")

    # Navigation Section
    st.markdown("---")
    st.markdown("### 🧭 Navigation")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔙 Back to Stage 8 (Optimization)", use_container_width=True):
            logger.info("User navigated back to Stage 8 from Script Browser")
            state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated back from Stage 9")
            st.rerun()
    
    with col2:
        if st.button("🔄 New Test Case (Stage 3)", use_container_width=True):
            logger.info("User chose to start new test case from Script Browser")
            state.advance_to(StateStage.STAGE3_CONVERT, "User chose new test case from Stage 9")
            st.rerun()
    
    with col3:
        if st.button("🏠 Start Over (Stage 1)", use_container_width=True):
            logger.info("User chose to start over from Script Browser")
            state.advance_to(StateStage.STAGE1_UPLOAD, "User chose to start over from Stage 9")
            st.rerun()
