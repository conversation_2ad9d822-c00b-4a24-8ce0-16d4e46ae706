#!/usr/bin/env python3
"""
Test script for Stage 1 redirection and unresponsiveness fixes.

This script tests the implemented fixes to ensure they work correctly:
1. Stage validation safety checks
2. Stage recovery mechanisms
3. Improved error handling
"""

import logging
import sys
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_stage1_fixes")


def test_state_validation_safety_checks():
    """Test the safety checks that prevent unnecessary Stage 1 fallbacks."""
    logger.info("=== TESTING STATE VALIDATION SAFETY CHECKS ===")
    
    try:
        # Import StateManager
        from state_manager import StateManager, StateStage
        
        # Create a test state
        state = StateManager()
        
        # Set up state as if we're in Stage 4 with valid data
        state.current_stage = StateStage.STAGE4_DETECT
        state.uploaded_excel = "test_file.xlsx"
        state.test_cases = [{"Test Case ID": "TC001", "Test Case Objective": "Test objective"}]
        state.website_url = "https://test.com"
        state.selected_test_case = {"Test Case ID": "TC001"}
        state.conversion_done = True
        state.step_table_json = [{"step": "test"}]
        
        logger.info(f"Initial state: {state.current_stage.get_display_name()}")
        
        # Test the safety check by calling update_stage_based_on_completion
        # This should NOT revert to Stage 1 because we have file and test cases
        original_stage = state.current_stage
        updated = state.update_stage_based_on_completion()
        
        if state.current_stage == original_stage:
            logger.info("✅ Safety check working: Stage 4 preserved despite validation")
        else:
            logger.warning(f"⚠️ Stage changed from {original_stage.get_display_name()} to {state.current_stage.get_display_name()}")
        
        # Test with missing file - this SHOULD revert to Stage 1
        state.uploaded_excel = None
        state.uploaded_file = None
        state.test_cases = None
        
        updated = state.update_stage_based_on_completion()
        
        if state.current_stage == StateStage.STAGE1_UPLOAD:
            logger.info("✅ Proper fallback: Reverted to Stage 1 when file/test cases missing")
        else:
            logger.warning(f"⚠️ Expected Stage 1, got {state.current_stage.get_display_name()}")
        
        logger.info("✅ State validation safety checks test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ State validation safety checks test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_stage_advance_validation():
    """Test the improved stage advancement validation."""
    logger.info("=== TESTING STAGE ADVANCEMENT VALIDATION ===")
    
    try:
        from state_manager import StateManager, StateStage
        
        # Create a test state
        state = StateManager()
        state.current_stage = StateStage.STAGE1_UPLOAD
        
        # Test valid advancement
        success = state.advance_to(StateStage.STAGE2_WEBSITE, "Test advancement")
        
        if success and state.current_stage == StateStage.STAGE2_WEBSITE:
            logger.info("✅ Valid stage advancement working correctly")
        else:
            logger.warning(f"⚠️ Stage advancement failed or incorrect: success={success}, stage={state.current_stage.get_display_name()}")
        
        # Test invalid advancement (should fail)
        success = state.advance_to(StateStage.STAGE1_UPLOAD, "Invalid backward transition")
        
        if not success:
            logger.info("✅ Invalid stage transition properly rejected")
        else:
            logger.warning("⚠️ Invalid stage transition was allowed")
        
        logger.info("✅ Stage advancement validation test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Stage advancement validation test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_stage1_import():
    """Test that Stage 1 module can be imported without errors."""
    logger.info("=== TESTING STAGE 1 MODULE IMPORT ===")
    
    try:
        # Try to import Stage 1 module
        import stages.stage1
        logger.info("✅ stages.stage1 imported successfully")
        
        # Check if the main function exists
        if hasattr(stages.stage1, 'stage1_upload_excel'):
            logger.info("✅ stage1_upload_excel function found")
        else:
            logger.warning("⚠️ stage1_upload_excel function not found")
        
        # Check if parse_excel_cached function exists
        if hasattr(stages.stage1, 'parse_excel_cached'):
            logger.info("✅ parse_excel_cached function found")
        else:
            logger.warning("⚠️ parse_excel_cached function not found")
        
        logger.info("✅ Stage 1 module import test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Stage 1 module import test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_app_fallback_logic():
    """Test the improved app fallback logic."""
    logger.info("=== TESTING APP FALLBACK LOGIC ===")
    
    try:
        # Check that app.py has the improved fallback logic
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key improvements
        improvements = [
            "attempting stage recovery",
            "update_stage_based_on_completion",
            "Stage recovery successful",
            "Application state issue detected"
        ]
        
        found_improvements = []
        for improvement in improvements:
            if improvement in content:
                found_improvements.append(improvement)
        
        logger.info(f"Found {len(found_improvements)}/{len(improvements)} expected improvements:")
        for improvement in found_improvements:
            logger.info(f"  ✅ {improvement}")
        
        missing_improvements = set(improvements) - set(found_improvements)
        for improvement in missing_improvements:
            logger.warning(f"  ⚠️ Missing: {improvement}")
        
        if len(found_improvements) >= 3:
            logger.info("✅ App fallback logic improvements detected")
            return True
        else:
            logger.warning("⚠️ Some app fallback logic improvements missing")
            return False
        
    except Exception as e:
        logger.error(f"❌ App fallback logic test failed: {e}")
        return False


def main():
    """Main test function."""
    logger.info("=== STAGE 1 FIXES VERIFICATION ===")
    
    all_passed = True
    
    # Run all tests
    tests = [
        test_state_validation_safety_checks,
        test_stage_advance_validation,
        test_stage1_import,
        test_app_fallback_logic
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            if not result:
                all_passed = False
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
            all_passed = False
    
    # Summary
    logger.info("=== TEST SUMMARY ===")
    passed_count = sum(results)
    total_count = len(results)
    
    logger.info(f"Tests passed: {passed_count}/{total_count}")
    
    if all_passed:
        logger.info("🎉 All tests passed! Stage 1 fixes are working correctly.")
        logger.info("✅ The Stage 1 redirection and unresponsiveness issues should be resolved.")
    else:
        logger.warning("⚠️ Some tests failed. Please review the issues above.")
    
    # Recommendations
    logger.info("=== RECOMMENDATIONS ===")
    logger.info("1. Test file upload functionality in the actual application")
    logger.info("2. Monitor application logs for stage transition messages")
    logger.info("3. Verify that Stage 1 remains responsive after redirection")
    logger.info("4. Test stage recovery mechanisms with various state scenarios")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
