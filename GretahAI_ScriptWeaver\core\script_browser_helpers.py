"""
Helper functions for the Script Browser and Comparison functionality (Stage 9).

This module provides functions for:
1. Filtering and searching script history
2. Generating comparison views between script versions
3. Creating metadata displays for scripts
4. Handling script downloads and exports
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import difflib

# Set up logging
logger = logging.getLogger(__name__)


def filter_scripts_by_criteria(script_history: List[Dict[str, Any]], 
                              script_type: str = None, 
                              test_case_id: str = None, 
                              step_no: str = None,
                              date_range: Tuple[datetime, datetime] = None) -> List[Dict[str, Any]]:
    """
    Filter script history based on various criteria.
    
    Args:
        script_history: List of script entries from StateManager
        script_type: Filter by script type ('step', 'combined', 'optimized')
        test_case_id: Filter by test case ID
        step_no: Filter by step number
        date_range: Tuple of (start_date, end_date) for filtering
        
    Returns:
        Filtered list of script entries
    """
    try:
        filtered_scripts = script_history.copy()
        
        # Filter by script type
        if script_type:
            filtered_scripts = [s for s in filtered_scripts if s.get('type') == script_type]
            
        # Filter by test case ID
        if test_case_id:
            filtered_scripts = [s for s in filtered_scripts if s.get('test_case_id') == test_case_id]
            
        # Filter by step number
        if step_no:
            filtered_scripts = [s for s in filtered_scripts if s.get('step_no') == step_no]
            
        # Filter by date range
        if date_range:
            start_date, end_date = date_range
            filtered_scripts = [s for s in filtered_scripts 
                              if start_date <= s.get('timestamp', datetime.min) <= end_date]
        
        logger.info(f"Filtered {len(script_history)} scripts to {len(filtered_scripts)} based on criteria")
        return filtered_scripts
        
    except Exception as e:
        logger.error(f"Error filtering scripts: {e}")
        return script_history


def search_scripts_by_content(script_history: List[Dict[str, Any]], 
                             search_term: str, 
                             case_sensitive: bool = False) -> List[Dict[str, Any]]:
    """
    Search scripts by content.
    
    Args:
        script_history: List of script entries
        search_term: Term to search for in script content
        case_sensitive: Whether search should be case sensitive
        
    Returns:
        List of scripts containing the search term
    """
    try:
        if not search_term.strip():
            return script_history
            
        search_term = search_term if case_sensitive else search_term.lower()
        matching_scripts = []
        
        for script in script_history:
            content = script.get('content', '')
            content = content if case_sensitive else content.lower()
            
            if search_term in content:
                matching_scripts.append(script)
                
        logger.info(f"Found {len(matching_scripts)} scripts matching search term: '{search_term}'")
        return matching_scripts
        
    except Exception as e:
        logger.error(f"Error searching scripts: {e}")
        return script_history


def generate_script_comparison(script1: Dict[str, Any], 
                              script2: Dict[str, Any], 
                              comparison_type: str = "side_by_side") -> str:
    """
    Generate a comparison between two scripts.
    
    Args:
        script1: First script entry
        script2: Second script entry
        comparison_type: Type of comparison ('side_by_side', 'unified', 'context')
        
    Returns:
        HTML string containing the comparison
    """
    try:
        content1 = script1.get('content', '')
        content2 = script2.get('content', '')
        
        # Split into lines for comparison
        lines1 = content1.splitlines()
        lines2 = content2.splitlines()
        
        # Generate comparison based on type
        if comparison_type == "side_by_side":
            diff = difflib.HtmlDiff(tabsize=4, wrapcolumn=80)
            html_diff = diff.make_file(
                lines1,
                lines2,
                fromdesc=f"{script1.get('type', 'Script')} - {script1.get('timestamp', 'Unknown')}",
                todesc=f"{script2.get('type', 'Script')} - {script2.get('timestamp', 'Unknown')}",
                context=True,
                numlines=3
            )
            
            # Enhance with custom styling for Streamlit
            enhanced_diff = html_diff.replace(
                '<style type="text/css">',
                '''<style type="text/css">
                /* Enhanced styles for script comparison */
                table.diff {width: 100%; border-collapse: collapse; font-family: 'Courier New', monospace; font-size: 13px;}
                table.diff td {padding: 2px 4px; vertical-align: top; white-space: pre-wrap; word-wrap: break-word;}
                table.diff .diff_add {background-color: #d4edda; color: #155724; border-left: 3px solid #28a745;}
                table.diff .diff_chg {background-color: #fff3cd; color: #856404; border-left: 3px solid #ffc107;}
                table.diff .diff_sub {background-color: #f8d7da; color: #721c24; border-left: 3px solid #dc3545;}
                table.diff th {background-color: #e9ecef; padding: 8px; text-align: center; font-weight: bold;}
                .diff_header {background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px;}
                '''
            )
            
            return enhanced_diff
            
        elif comparison_type == "unified":
            # Generate unified diff
            unified_diff = list(difflib.unified_diff(
                lines1,
                lines2,
                fromfile=f"{script1.get('type', 'Script')} ({script1.get('id', 'Unknown')})",
                tofile=f"{script2.get('type', 'Script')} ({script2.get('id', 'Unknown')})",
                lineterm=""
            ))
            
            # Convert to HTML with syntax highlighting
            html_lines = ['<pre style="font-family: monospace; font-size: 14px; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">']
            
            for line in unified_diff:
                if line.startswith('+++') or line.startswith('---'):
                    html_lines.append(f'<span style="font-weight: bold; color: #6c757d;">{line}</span>')
                elif line.startswith('@@'):
                    html_lines.append(f'<span style="font-weight: bold; color: #007bff;">{line}</span>')
                elif line.startswith('+'):
                    html_lines.append(f'<span style="background-color: #d4edda; color: #155724;">{line}</span>')
                elif line.startswith('-'):
                    html_lines.append(f'<span style="background-color: #f8d7da; color: #721c24;">{line}</span>')
                else:
                    html_lines.append(line)
                    
            html_lines.append('</pre>')
            return '\n'.join(html_lines)
            
        else:  # context diff
            context_diff = list(difflib.context_diff(
                lines1,
                lines2,
                fromfile=f"{script1.get('type', 'Script')} ({script1.get('id', 'Unknown')})",
                tofile=f"{script2.get('type', 'Script')} ({script2.get('id', 'Unknown')})",
                lineterm=""
            ))
            
            return '<pre style="font-family: monospace; font-size: 14px;">' + '\n'.join(context_diff) + '</pre>'
            
    except Exception as e:
        logger.error(f"Error generating script comparison: {e}")
        return f"<p>Error generating comparison: {str(e)}</p>"


def format_script_metadata(script: Dict[str, Any]) -> Dict[str, str]:
    """
    Format script metadata for display.
    
    Args:
        script: Script entry from history
        
    Returns:
        Dictionary of formatted metadata
    """
    try:
        metadata = {
            'Script ID': script.get('id', 'Unknown'),
            'Type': script.get('type', 'Unknown').title(),
            'Test Case': script.get('test_case_id', 'Unknown'),
            'Step Number': script.get('step_no', 'N/A'),
            'Generated': script.get('timestamp', datetime.now()).strftime('%Y-%m-%d %H:%M:%S'),
            'File Size': f"{len(script.get('content', ''))} characters",
            'Line Count': f"{len(script.get('content', '').splitlines())} lines",
            'File Path': script.get('file_path', 'Not saved to file')
        }
        
        # Add custom metadata if available
        custom_metadata = script.get('metadata', {})
        for key, value in custom_metadata.items():
            metadata[f"Custom: {key}"] = str(value)
            
        return metadata
        
    except Exception as e:
        logger.error(f"Error formatting script metadata: {e}")
        return {'Error': str(e)}


def get_script_statistics(script_history: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate statistics about the script history.
    
    Args:
        script_history: List of script entries
        
    Returns:
        Dictionary containing various statistics
    """
    try:
        if not script_history:
            return {
                'total_scripts': 0,
                'script_types': {},
                'test_cases': set(),
                'date_range': None,
                'total_lines': 0,
                'total_size': 0
            }
            
        # Count script types
        script_types = {}
        test_cases = set()
        total_lines = 0
        total_size = 0
        timestamps = []
        
        for script in script_history:
            script_type = script.get('type', 'unknown')
            script_types[script_type] = script_types.get(script_type, 0) + 1
            
            test_case_id = script.get('test_case_id')
            if test_case_id:
                test_cases.add(test_case_id)
                
            content = script.get('content', '')
            total_lines += len(content.splitlines())
            total_size += len(content)
            
            timestamp = script.get('timestamp')
            if timestamp:
                timestamps.append(timestamp)
                
        # Calculate date range
        date_range = None
        if timestamps:
            date_range = {
                'earliest': min(timestamps),
                'latest': max(timestamps),
                'span_days': (max(timestamps) - min(timestamps)).days
            }
            
        return {
            'total_scripts': len(script_history),
            'script_types': script_types,
            'test_cases': list(test_cases),
            'test_case_count': len(test_cases),
            'date_range': date_range,
            'total_lines': total_lines,
            'total_size': total_size,
            'avg_script_size': total_size // len(script_history) if script_history else 0,
            'avg_script_lines': total_lines // len(script_history) if script_history else 0
        }
        
    except Exception as e:
        logger.error(f"Error generating script statistics: {e}")
        return {'error': str(e)}


def create_download_filename(script: Dict[str, Any], include_timestamp: bool = True) -> str:
    """
    Create a descriptive filename for script downloads.
    
    Args:
        script: Script entry
        include_timestamp: Whether to include timestamp in filename
        
    Returns:
        Formatted filename
    """
    try:
        script_type = script.get('type', 'script')
        test_case_id = script.get('test_case_id', 'unknown')
        step_no = script.get('step_no', '')
        
        # Base filename
        filename_parts = ['test', test_case_id, script_type]
        
        # Add step number if available
        if step_no:
            filename_parts.append(f"step{step_no}")
            
        # Add timestamp if requested
        if include_timestamp:
            timestamp = script.get('timestamp', datetime.now())
            timestamp_str = timestamp.strftime('%Y%m%d_%H%M%S')
            filename_parts.append(timestamp_str)
            
        filename = '_'.join(filename_parts) + '.py'
        
        # Clean filename (remove invalid characters)
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
            
        return filename
        
    except Exception as e:
        logger.error(f"Error creating download filename: {e}")
        return f"script_{int(datetime.now().timestamp())}.py"
